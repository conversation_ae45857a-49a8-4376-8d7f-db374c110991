/**
 * Migration script to convert old streak system to new activity-based system
 * Run this after deploying the new schema
 */

async function migrateStreakData(strapi) {
  console.log('🚀 Starting migration from old streak system to new activity system...');

  try {
    // Check if old tables exist
    const hasOldTables = await checkOldTablesExist(strapi);
    if (!hasOldTables) {
      console.log('✅ No old tables found, migration not needed');
      return;
    }

    // Step 1: Migrate streak_questions to activities
    await migrateStreakQuestions(strapi);

    // Step 2: Migrate questions_answers to user_responses
    await migrateQuestionsAnswers(strapi);

    // Step 3: Migrate streaks to user_activities
    await migrateStreaks(strapi);

    console.log('✅ Migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function checkOldTablesExist(strapi) {
  try {
    const result = await strapi.db.connection.raw(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
        AND table_name IN ('streak_questions', 'questions_answers', 'streaks')
    `);
    return result[0][0].count > 0;
  } catch (error) {
    console.log('Old tables do not exist, skipping migration');
    return false;
  }
}

async function migrateStreakQuestions(strapi) {
  console.log('📝 Migrating streak_questions to activities...');

  try {
    const streakQuestions = await strapi.db.connection.raw(`
      SELECT * FROM streak_questions
    `);

    for (const sq of streakQuestions[0]) {
      // Create activity for each streak question
      await strapi.entityService.create('api::activity.activity', {
        data: {
          title: `Streak - ${new Date(sq.value).toISOString().split('T')[0]}`,
          description: sq.description,
          activity_type: 'streak',
          course: sq.course_id,
          start_date: new Date(sq.value),
          settings: {
            date: new Date(sq.value).toISOString().split('T')[0],
            streak_config: {
              questions_per_session: 3,
              time_limit_per_question: 60
            },
            old_streak_question_id: sq.id // For reference
          },
          is_active: true
        }
      });
    }

    console.log(`✅ Migrated ${streakQuestions[0].length} streak questions to activities`);
  } catch (error) {
    console.error('Error migrating streak questions:', error);
    throw error;
  }
}

async function migrateQuestionsAnswers(strapi) {
  console.log('📝 Migrating questions_answers to user_responses...');

  try {
    // Get questions_answers with related data
    const questionsAnswers = await strapi.db.connection.raw(`
      SELECT 
        qa.*,
        qau.user_id,
        qaq.question_id
      FROM questions_answers qa
      LEFT JOIN questions_answers_user_lnk qau ON qa.id = qau.questions_answer_id
      LEFT JOIN questions_answers_question_lnk qaq ON qa.id = qaq.questions_answer_id
      WHERE qa.streak_question_id IS NOT NULL
    `);

    for (const qa of questionsAnswers[0]) {
      // Find corresponding activity
      const activities = await strapi.entityService.findMany('api::activity.activity', {
        filters: {
          activity_type: 'streak',
          settings: {
            old_streak_question_id: qa.streak_question_id
          }
        }
      });

      if (activities.length > 0) {
        const activity = activities[0];

        // Create user response
        await strapi.entityService.create('api::user-response.user-response', {
          data: {
            user: qa.user_id,
            activity: activity.id,
            question: qa.question_id,
            answer: qa.answer,
            is_correct: qa.is_correct,
            is_star_point: qa.is_star_point || false,
            points_earned: 0, // Will be calculated
            attempt_number: 1
          }
        });
      }
    }

    console.log(`✅ Migrated ${questionsAnswers[0].length} questions answers to user responses`);
  } catch (error) {
    console.error('Error migrating questions answers:', error);
    throw error;
  }
}

async function migrateStreaks(strapi) {
  console.log('📝 Migrating streaks to user_activities...');

  try {
    const streaks = await strapi.db.connection.raw(`
      SELECT * FROM streaks
    `);

    for (const streak of streaks[0]) {
      // Find corresponding activity
      const activities = await strapi.entityService.findMany('api::activity.activity', {
        filters: {
          activity_type: 'streak',
          settings: {
            old_streak_question_id: streak.streak_question_id
          }
        }
      });

      if (activities.length > 0) {
        const activity = activities[0];

        // Create user activity
        await strapi.entityService.create('api::user-activity.user-activity', {
          data: {
            user: streak.user_id,
            activity: activity.id,
            status: streak.isJoin ? 'completed' : 'in_progress',
            time_spent: streak.time || 0,
            is_streak_joined: streak.isJoin || false,
            started_at: streak.created_at,
            completed_at: streak.isJoin ? streak.updated_at : null
          }
        });
      }
    }

    console.log(`✅ Migrated ${streaks[0].length} streaks to user activities`);
  } catch (error) {
    console.error('Error migrating streaks:', error);
    throw error;
  }
}

// Export for use in bootstrap or manual execution
module.exports = {
  migrateStreakData
};

// If running directly
if (require.main === module) {
  console.log('This migration should be run through Strapi bootstrap or manually');
}
