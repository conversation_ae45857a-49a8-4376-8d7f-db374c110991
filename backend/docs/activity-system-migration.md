# Activity System Migration Guide

## 🎯 Overview

This document outlines the migration from the old streak-specific system to the new unified Activity system that supports streak, homework, exam, and practice activities.

## 📊 Schema Changes

### Old Schema
```
streak_questions -> activities
questions_answers -> user_responses  
streaks -> user_activities
```

### New Schema Structure

#### Activities Table
- **Purpose**: Central table for all learning activities
- **Types**: streak, homework, exam, practice
- **Key Fields**: 
  - `activity_type`: enum('streak', 'homework', 'exam', 'practice')
  - `settings`: JSON field for type-specific configurations
  - `questions`: Many-to-many relationship with questions

#### User Responses Table
- **Purpose**: Store user answers for any activity
- **Key Fields**:
  - `activity_id`: Links to specific activity
  - `question_id`: Links to specific question
  - `is_star_point`: For streak double points feature
  - `points_earned`: Calculated points

#### User Activities Table
- **Purpose**: Track user participation in activities
- **Key Fields**:
  - `status`: enum('not_started', 'in_progress', 'completed', 'abandoned')
  - `is_streak_joined`: Boolean for streak completion
  - `time_spent`: Time in seconds

## 🔄 API Changes

### Backward Compatible Endpoints

All existing streak endpoints remain functional:
- `POST /streaks/get-streak-by-user`
- `POST /streaks/get-total-rollup`
- `POST /streaks/get-data-finish`
- `POST /streaks/save-question-answer`

### New Endpoints

#### Activities
- `GET /activities/by-type?activity_type=streak&course_id=1`
- `GET /activities/today-streak`
- `POST /activities/create-streak`

#### User Responses
- `POST /user-responses/save-answer`
- `GET /user-responses/by-activity/:activity_id`
- `GET /user-responses/streak-stats`

#### User Activities
- `POST /user-activities/start`
- `POST /user-activities/complete`
- `GET /user-activities/streak-stats`

## 🚀 Frontend Integration

### Updated strapi.js Methods

```javascript
// Backward compatible - existing code works
const result = await strapi.streak.createStreak({
  activity_id: activityId // instead of streak_question_id
});

// New methods available
const activity = await strapi.activity.getTodayStreak(courseId);
const response = await strapi.userResponse.saveAnswer({
  activity_id: activityId,
  question_id: questionId,
  answer: 'A',
  is_star_point: false
});
```

### Migration for Frontend Components

1. **Replace `streak_question_id` with `activity_id`**
2. **Use new API methods for better performance**
3. **Leverage new features like progress tracking**

## 📈 Benefits of New System

### 1. Scalability
- Easy to add homework, exam, practice
- Unified data structure
- Consistent API patterns

### 2. Better Analytics
- Comprehensive user progress tracking
- Performance metrics per activity type
- Learning analytics capabilities

### 3. Maintainability
- Single codebase for all activity types
- Consistent naming conventions
- Better separation of concerns

## 🔧 Migration Steps

### 1. Database Migration
```bash
# Run migration script (if old data exists)
node backend/database/migrations/migrate-to-activity-schema.js
```

### 2. Update Frontend Code
```javascript
// Old way
const streakId = params.streak_question_id;

// New way  
const activityId = params.activity_id || params.streak_question_id; // backward compatible
```

### 3. Test Thoroughly
- Verify all streak functionality works
- Test new API endpoints
- Validate data integrity

## 🎪 Future Expansion

### Adding Homework
```javascript
// Create homework activity
const homework = await strapi.activity.create({
  title: "Bài tập chương 1",
  activity_type: "homework",
  course_id: 1,
  settings: {
    due_date: "2024-02-01",
    max_attempts: 3
  }
});
```

### Adding Exams
```javascript
// Create exam activity
const exam = await strapi.activity.create({
  title: "Kiểm tra giữa kỳ",
  activity_type: "exam", 
  course_id: 1,
  settings: {
    time_limit: 90, // minutes
    randomize_questions: true
  }
});
```

## 🛡️ Data Integrity

### Validation Rules
- Activity must have valid type
- User responses must link to valid activity and question
- Time tracking must be non-negative
- Streak completion requires all questions answered

### Constraints
- Unique constraint on (user_id, activity_id) for user_activities
- Foreign key constraints maintained
- Proper indexing for performance

## 📝 Testing Checklist

- [ ] Existing streak functionality works
- [ ] New API endpoints respond correctly
- [ ] Data migration completed successfully
- [ ] Frontend components updated
- [ ] Performance benchmarks met
- [ ] Error handling works properly

## 🔍 Troubleshooting

### Common Issues

1. **Old endpoints not working**
   - Check if controllers are updated
   - Verify route configurations

2. **Data not migrating**
   - Run migration script manually
   - Check database permissions

3. **Frontend errors**
   - Update API calls to use new format
   - Check for missing activity_id parameters

### Debug Commands

```bash
# Check if new tables exist
SHOW TABLES LIKE '%activities%';

# Verify data migration
SELECT COUNT(*) FROM activities WHERE activity_type = 'streak';

# Test API endpoints
curl -X GET "http://localhost:1337/api/activities/today-streak"
```

## 📞 Support

For issues with the migration:
1. Check this documentation first
2. Review error logs in backend/logs/
3. Test with Postman/curl for API issues
4. Contact development team if needed

---

*This migration maintains full backward compatibility while enabling future expansion to homework, exam, and practice activities.*
