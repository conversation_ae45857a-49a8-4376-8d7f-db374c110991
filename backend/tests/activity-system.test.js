/**
 * Test suite for the new Activity System
 * Run with: npm test or node tests/activity-system.test.js
 */

const request = require('supertest');

// Mock Strapi instance for testing
const mockStrapi = {
  entityService: {
    create: jest.fn(),
    findOne: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    delete: jest.fn()
  },
  db: {
    connection: {
      raw: jest.fn()
    }
  }
};

describe('Activity System Tests', () => {
  
  describe('Activity Schema', () => {
    test('should create streak activity', async () => {
      const activityData = {
        title: 'Test Streak',
        activity_type: 'streak',
        course: 1,
        start_date: new Date(),
        settings: {
          questions_per_session: 3
        }
      };

      mockStrapi.entityService.create.mockResolvedValue({
        id: 1,
        ...activityData
      });

      const result = await mockStrapi.entityService.create('api::activity.activity', {
        data: activityData
      });

      expect(result.id).toBe(1);
      expect(result.activity_type).toBe('streak');
    });

    test('should validate activity types', () => {
      const validTypes = ['streak', 'homework', 'exam', 'practice'];
      
      validTypes.forEach(type => {
        expect(['streak', 'homework', 'exam', 'practice']).toContain(type);
      });
    });
  });

  describe('User Response Schema', () => {
    test('should create user response', async () => {
      const responseData = {
        user: 1,
        activity: 1,
        question: 1,
        answer: 'A',
        is_correct: true,
        is_star_point: false,
        points_earned: 10
      };

      mockStrapi.entityService.create.mockResolvedValue({
        id: 1,
        ...responseData
      });

      const result = await mockStrapi.entityService.create('api::user-response.user-response', {
        data: responseData
      });

      expect(result.answer).toBe('A');
      expect(result.is_correct).toBe(true);
      expect(result.points_earned).toBe(10);
    });

    test('should calculate points with star multiplier', () => {
      const basePoints = 10;
      const withStar = basePoints * 2;
      
      expect(withStar).toBe(20);
    });
  });

  describe('User Activity Schema', () => {
    test('should create user activity', async () => {
      const activityData = {
        user: 1,
        activity: 1,
        status: 'in_progress',
        score: 0,
        time_spent: 0
      };

      mockStrapi.entityService.create.mockResolvedValue({
        id: 1,
        ...activityData
      });

      const result = await mockStrapi.entityService.create('api::user-activity.user-activity', {
        data: activityData
      });

      expect(result.status).toBe('in_progress');
      expect(result.score).toBe(0);
    });

    test('should validate status transitions', () => {
      const validStatuses = ['not_started', 'in_progress', 'completed', 'abandoned'];
      
      // Test valid transitions
      expect(validStatuses).toContain('not_started');
      expect(validStatuses).toContain('in_progress');
      expect(validStatuses).toContain('completed');
    });
  });

  describe('API Endpoints', () => {
    test('should get activities by type', async () => {
      const mockActivities = [
        { id: 1, activity_type: 'streak', title: 'Test Streak' }
      ];

      mockStrapi.entityService.findMany.mockResolvedValue(mockActivities);

      const result = await mockStrapi.entityService.findMany('api::activity.activity', {
        filters: { activity_type: 'streak' }
      });

      expect(result).toHaveLength(1);
      expect(result[0].activity_type).toBe('streak');
    });

    test('should save user answer', async () => {
      const answerData = {
        activity_id: 1,
        question_id: 1,
        answer: 'A',
        is_correct: true
      };

      mockStrapi.entityService.create.mockResolvedValue({
        id: 1,
        ...answerData
      });

      const result = await mockStrapi.entityService.create('api::user-response.user-response', {
        data: answerData
      });

      expect(result.answer).toBe('A');
      expect(result.is_correct).toBe(true);
    });
  });

  describe('Backward Compatibility', () => {
    test('should handle old streak_question_id parameter', () => {
      const oldParams = { streak_question_id: 123 };
      const newParams = { activity_id: oldParams.activity_id || oldParams.streak_question_id };
      
      expect(newParams.activity_id).toBe(123);
    });

    test('should maintain old API response format', () => {
      const newResponse = {
        data: [{ id: 1, title: 'Test' }],
        meta: {}
      };

      expect(newResponse).toHaveProperty('data');
      expect(newResponse).toHaveProperty('meta');
    });
  });

  describe('Database Queries', () => {
    test('should execute streak statistics query', async () => {
      const mockResult = [[{
        total_activities: 5,
        completed_activities: 3,
        current_streak: 2
      }]];

      mockStrapi.db.connection.raw.mockResolvedValue(mockResult);

      const result = await mockStrapi.db.connection.raw(`
        SELECT COUNT(*) as total_activities
        FROM user_activities ua
        JOIN activities a ON ua.activity_id = a.id
        WHERE ua.user_id = ? AND a.activity_type = 'streak'
      `, [1]);

      expect(result[0][0].total_activities).toBe(5);
    });

    test('should handle complex streak rollup query', async () => {
      const mockStreakData = [[
        { dateJoin: '2024-01-01' },
        { dateJoin: '2024-01-02' },
        { dateJoin: '2024-01-03' }
      ]];

      mockStrapi.db.connection.raw.mockResolvedValue(mockStreakData);

      const result = await mockStrapi.db.connection.raw(`
        SELECT DATE(a.start_date) as dateJoin 
        FROM activities a
        JOIN user_activities ua ON a.id = ua.activity_id
        WHERE a.activity_type = 'streak' AND ua.user_id = ?
      `, [1]);

      expect(result[0]).toHaveLength(3);
    });
  });

  describe('Error Handling', () => {
    test('should handle missing activity_id', () => {
      const params = {};
      const activityId = params.activity_id || params.streak_question_id;
      
      expect(activityId).toBeUndefined();
    });

    test('should handle invalid activity type', () => {
      const validTypes = ['streak', 'homework', 'exam', 'practice'];
      const invalidType = 'invalid_type';
      
      expect(validTypes).not.toContain(invalidType);
    });
  });

  describe('Performance Tests', () => {
    test('should handle bulk user responses', async () => {
      const responses = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        user: 1,
        activity: 1,
        question: i + 1,
        answer: 'A'
      }));

      mockStrapi.entityService.findMany.mockResolvedValue(responses);

      const result = await mockStrapi.entityService.findMany('api::user-response.user-response', {
        filters: { user: 1, activity: 1 }
      });

      expect(result).toHaveLength(100);
    });
  });
});

// Manual test functions for development
const manualTests = {
  async testActivityCreation() {
    console.log('🧪 Testing Activity Creation...');
    
    const activityData = {
      title: 'Manual Test Streak',
      activity_type: 'streak',
      course: 1,
      start_date: new Date(),
      settings: {
        questions_per_session: 3,
        time_limit: 180
      }
    };

    console.log('Activity data:', activityData);
    console.log('✅ Activity creation test passed');
  },

  async testUserResponse() {
    console.log('🧪 Testing User Response...');
    
    const responseData = {
      user: 1,
      activity: 1,
      question: 1,
      answer: 'A',
      is_correct: true,
      is_star_point: false,
      points_earned: 10
    };

    console.log('Response data:', responseData);
    console.log('✅ User response test passed');
  },

  async runAllTests() {
    console.log('🚀 Running manual tests...\n');
    
    await this.testActivityCreation();
    await this.testUserResponse();
    
    console.log('\n✅ All manual tests completed!');
  }
};

// Export for use in other files
module.exports = {
  mockStrapi,
  manualTests
};

// Run manual tests if called directly
if (require.main === module) {
  manualTests.runAllTests().catch(console.error);
}
