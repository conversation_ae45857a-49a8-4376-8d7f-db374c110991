/**
 * user-activity router
 */

export default {
  routes: [
    // Default CRUD routes
    {
      method: 'GET',
      path: '/user-activities',
      handler: 'user-activity.find',
    },
    {
      method: 'GET',
      path: '/user-activities/:id',
      handler: 'user-activity.findOne',
    },
    {
      method: 'POST',
      path: '/user-activities',
      handler: 'user-activity.create',
    },
    {
      method: 'PUT',
      path: '/user-activities/:id',
      handler: 'user-activity.update',
    },
    {
      method: 'DELETE',
      path: '/user-activities/:id',
      handler: 'user-activity.delete',
    },

    // Custom routes
    {
      method: 'POST',
      path: '/user-activities/start',
      handler: 'user-activity.startActivity',
    },
    {
      method: 'POST',
      path: '/user-activities/update-progress',
      handler: 'user-activity.updateProgress',
    },
    {
      method: 'POST',
      path: '/user-activities/complete',
      handler: 'user-activity.completeActivity',
    },
    {
      method: 'GET',
      path: '/user-activities/streak-stats',
      handler: 'user-activity.getStreakStats',
    },
  ],
};
