/**
 * user-activity controller
 */

// @ts-nocheck
import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::user-activity.user-activity' as any, ({ strapi }) => ({
  // Start an activity
  async startActivity(ctx) {
    const { activity_id } = ctx.request.body;
    const userId = ctx.state.user.id;

    try {
      // Check if user already started this activity
      const existingActivity: any = await strapi.entityService.findMany('api::user-activity.user-activity' as any, {
        filters: {
          user: userId,
          activity: activity_id
        }
      });

      let userActivity;

      if (existingActivity.length > 0) {
        // Update existing activity if not completed
        if (existingActivity[0].status !== 'completed') {
          userActivity = await strapi.entityService.update('api::user-activity.user-activity' as any, existingActivity[0].id, {
            data: {
              status: 'in_progress',
              started_at: new Date(),
              attempts_count: existingActivity[0].attempts_count + 1
            }
          });
        } else {
          userActivity = existingActivity[0];
        }
      } else {
        // Create new user activity
        userActivity = await strapi.entityService.create('api::user-activity.user-activity' as any, {
          data: {
            user: userId,
            activity: activity_id,
            status: 'in_progress',
            started_at: new Date(),
            attempts_count: 1
          }
        });
      }

      // Get activity details
      const activity: any = await strapi.entityService.findOne('api::activity.activity' as any, activity_id, {
        populate: {
          questions: true
        }
      });

      ctx.body = {
        data: {
          ...userActivity,
          activity: {
            id: activity.id,
            title: activity.title,
            activity_type: activity.activity_type,
            questions_count: activity.questions?.length || 0
          }
        },
        meta: {}
      };
    } catch (error) {
      ctx.badRequest('Failed to start activity', { error: error.message });
    }
  },

  // Update activity progress
  async updateProgress(ctx) {
    const { activity_id, time_spent, progress_percentage } = ctx.request.body;
    const userId = ctx.state.user.id;

    try {
      // Find user activity
      const userActivities = await strapi.entityService.findMany('api::user-activity.user-activity', {
        filters: {
          user: userId,
          activity: activity_id
        }
      });

      if (userActivities.length === 0) {
        return ctx.badRequest('User activity not found');
      }

      const userActivity = await strapi.entityService.update('api::user-activity.user-activity', userActivities[0].id, {
        data: {
          time_spent,
          progress_percentage
        }
      });

      ctx.body = {
        data: userActivity,
        meta: {}
      };
    } catch (error) {
      ctx.badRequest('Failed to update progress', { error: error.message });
    }
  },

  // Complete an activity
  async completeActivity(ctx) {
    const { activity_id, time_spent } = ctx.request.body;
    const userId = ctx.state.user.id;

    try {
      // Find user activity
      const userActivities = await strapi.entityService.findMany('api::user-activity.user-activity', {
        filters: {
          user: userId,
          activity: activity_id
        }
      });

      if (userActivities.length === 0) {
        return ctx.badRequest('User activity not found');
      }

      // Calculate score from user responses
      const userResponses = await strapi.entityService.findMany('api::user-response.user-response', {
        filters: {
          user: userId,
          activity: activity_id
        }
      });

      const totalScore = userResponses.reduce((sum, response) => sum + (response.points_earned || 0), 0);

      // Get activity to check type
      const activity = await strapi.entityService.findOne('api::activity.activity', activity_id);
      
      // For streak activities, set is_streak_joined to true
      const isStreakJoined = activity.activity_type === 'streak';

      // Update user activity
      const userActivity = await strapi.entityService.update('api::user-activity.user-activity', userActivities[0].id, {
        data: {
          status: 'completed',
          score: totalScore,
          time_spent,
          completed_at: new Date(),
          progress_percentage: 100,
          is_streak_joined: isStreakJoined
        }
      });

      ctx.body = {
        data: userActivity,
        meta: {
          total_score: totalScore,
          responses_count: userResponses.length
        }
      };
    } catch (error) {
      ctx.badRequest('Failed to complete activity', { error: error.message });
    }
  },

  // Get streak statistics
  async getStreakStats(ctx) {
    const userId = ctx.state.user.id;

    try {
      // Get current streak
      const streakService = strapi.service('api::activity.activity');
      const currentStreak = await streakService.getCurrentStreakCount(userId);

      // Get streak activities
      const rawQuery = `
        SELECT 
          DATE(a.start_date) as streak_date,
          ua.status,
          ua.score,
          ua.time_spent
        FROM activities a
        JOIN user_activities ua ON a.id = ua.activity_id
        WHERE a.activity_type = 'streak' 
          AND ua.user_id = ?
        ORDER BY a.start_date DESC
        LIMIT 30
      `;

      const result = await strapi.db.connection.raw(rawQuery, [userId]);
      const streakHistory = result[0] || [];

      ctx.body = {
        data: {
          current_streak: currentStreak,
          streak_history: streakHistory
        },
        meta: {}
      };
    } catch (error) {
      ctx.badRequest('Failed to fetch streak stats', { error: error.message });
    }
  }
}));
