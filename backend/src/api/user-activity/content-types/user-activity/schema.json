{"kind": "collectionType", "collectionName": "user_activities", "info": {"singularName": "user-activity", "pluralName": "user-activities", "displayName": "User Activity", "description": "Tr<PERSON>ng thái tham gia hoạt động của user"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "user_activities"}, "activity": {"type": "relation", "relation": "manyToOne", "target": "api::activity.activity", "inversedBy": "user_activities"}, "status": {"type": "enumeration", "enum": ["not_started", "in_progress", "completed", "abandoned"], "default": "not_started"}, "score": {"type": "integer", "default": 0, "description": "<PERSON><PERSON><PERSON> điểm đạ<PERSON> đ<PERSON>"}, "time_spent": {"type": "integer", "default": 0, "description": "<PERSON><PERSON><PERSON><PERSON> gian làm bài (giây)"}, "started_at": {"type": "datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "completed_at": {"type": "datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian hoàn thành"}, "progress_percentage": {"type": "decimal", "default": 0, "description": "<PERSON><PERSON><PERSON> tr<PERSON>m hoàn thành"}, "attempts_count": {"type": "integer", "default": 1, "description": "<PERSON><PERSON> lần thử"}, "is_streak_joined": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> tham gia <PERSON> hôm nay chưa"}, "metadata": {"type": "json", "description": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung"}}}