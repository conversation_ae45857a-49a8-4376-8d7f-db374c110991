{"kind": "collectionType", "collectionName": "questions", "info": {"singularName": "question", "pluralName": "questions", "displayName": "Question", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"questions_topics": {"type": "relation", "relation": "manyToMany", "target": "api::questions-topic.questions-topic", "inversedBy": "questions"}, "image_path": {"type": "string"}, "content": {"type": "text"}, "A": {"type": "string"}, "B": {"type": "string"}, "C": {"type": "string"}, "D": {"type": "string"}, "explain": {"type": "text"}, "exercise_type": {"type": "relation", "relation": "manyToOne", "target": "api::exercise-type.exercise-type", "inversedBy": "questions"}, "type": {"type": "enumeration", "enum": ["TN_4", "TN_2", "TN_value"]}, "knowledge_questions": {"type": "relation", "relation": "oneToMany", "target": "api::exercise.exercise", "mappedBy": "question"}, "question_status": {"type": "enumeration", "enum": ["todo", "checking", "approval", "reject"]}, "correct_answer": {"type": "string"}, "grade": {"type": "relation", "relation": "oneToOne", "target": "api::grade.grade"}, "chapter": {"type": "relation", "relation": "oneToOne", "target": "api::chapter.chapter"}, "correct_answer_type": {"type": "string"}, "activities": {"type": "relation", "relation": "manyToMany", "target": "api::activity.activity", "inversedBy": "questions"}, "user_responses": {"type": "relation", "relation": "oneToMany", "target": "api::user-response.user-response", "mappedBy": "question"}}}