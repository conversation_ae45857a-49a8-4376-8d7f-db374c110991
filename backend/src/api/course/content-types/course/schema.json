{"kind": "collectionType", "collectionName": "courses", "info": {"singularName": "course", "pluralName": "courses", "displayName": "Course", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "url_video_demo": {"type": "text"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "duration": {"type": "string"}, "price_description": {"type": "text"}, "orders": {"type": "relation", "relation": "oneToMany", "target": "api::order.order", "mappedBy": "course"}, "video_categories": {"type": "relation", "relation": "manyToMany", "target": "api::video-category.video-category", "mappedBy": "courses"}, "slug": {"type": "uid", "targetField": "title"}, "faqs": {"type": "relation", "relation": "oneToMany", "target": "api::faq.faq", "mappedBy": "course"}, "chapters": {"type": "relation", "relation": "oneToMany", "target": "api::chapter.chapter", "mappedBy": "course"}, "gradeId": {"type": "integer"}, "grade": {"type": "relation", "relation": "oneToOne", "target": "api::grade.grade", "mappedBy": "course"}, "features": {"type": "relation", "relation": "manyToMany", "target": "api::feature.feature", "mappedBy": "courses"}, "vouchers": {"type": "relation", "relation": "manyToMany", "target": "api::voucher.voucher", "mappedBy": "courses"}, "course_tiers": {"type": "relation", "relation": "oneToMany", "target": "api::course-tier.course-tier", "mappedBy": "course"}, "collection_video_id": {"type": "string"}, "link_active": {"type": "string"}, "link_unactive": {"type": "string"}, "link_livestream": {"type": "string"}, "start_date_live": {"type": "datetime"}, "end_date_live": {"type": "datetime"}, "next_date_live": {"type": "datetime"}, "class_schedules": {"type": "relation", "relation": "manyToMany", "target": "api::class-schedule.class-schedule", "mappedBy": "courses"}, "activities": {"type": "relation", "relation": "oneToMany", "target": "api::activity.activity", "mappedBy": "course"}}}