export default {
    routes: [
        {
            method: 'GET',
            path: '/streaks/raw-total',
            handler: 'streak.rawTotal',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-streak-by-user',
            handler: 'streak.getStreakByUser',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-total-rollup',
            handler: 'streak.getTotalRollup',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-data-finish',
            handler: 'streak.getDataFinish',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // New routes for backward compatibility
        {
            method: 'GET',
            path: '/streaks/today-activity',
            handler: 'streak.getTodayStreakActivity',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/save-question-answer',
            handler: 'streak.saveQuestionAnswer',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/create-streak',
            handler: 'streak.createStreak',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/update-time',
            handler: 'streak.updateTime',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/streaks/questions/:id',
            handler: 'streak.getQuestionByStreak',
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};