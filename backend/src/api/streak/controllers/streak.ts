/**
 * streak controller - Updated to use new Activity schema
 */

// @ts-nocheck
import {factories} from '@strapi/strapi'

export default factories.createCoreController('api::streak.streak', ({strapi}) => ({
    // Get total streak activities for user
    async rawTotal(ctx) {
        const user_id = ctx.state.user?.id || 4; // Use authenticated user
        const rawQuery = `
            SELECT COUNT(*) as total
            FROM user_activities ua
            JOIN activities a ON ua.activity_id = a.id
            WHERE ua.user_id = ? AND a.activity_type = 'streak'
        `;
        const result = await strapi.db.connection.raw(rawQuery, [user_id]);
        ctx.body = {
            data: {total: result[0]?.[0]?.total || 0},
            meta: {}
        };
    },

    // Create or get today's streak activity
    async getTodayStreakActivity(ctx) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        try {
            // Check if today's streak activity exists
            let streakActivity = await strapi.entityService.findMany('api::activity.activity', {
                filters: {
                    activity_type: 'streak',
                    start_date: {
                        $gte: today.toISOString(),
                        $lt: tomorrow.toISOString()
                    }
                },
                populate: {
                    questions: {
                        populate: {
                            exercise_type: true,
                            questions_topics: true
                        }
                    }
                }
            });

            if (streakActivity.length === 0) {
                // Create today's streak activity if not exists
                // This should be done by admin/system, but for backward compatibility
                streakActivity = await strapi.entityService.create('api::activity.activity', {
                    data: {
                        title: `Streak - ${today.toISOString().split('T')[0]}`,
                        activity_type: 'streak',
                        start_date: today,
                        settings: {
                            date: today.toISOString().split('T')[0],
                            questions_per_session: 3
                        },
                        is_active: true
                    }
                });
                streakActivity = [streakActivity];
            }

            ctx.body = {
                data: streakActivity[0],
                meta: {}
            };
        } catch (error) {
            ctx.badRequest('Failed to get streak activity', { error: error.message });
        }
    },
    // Get streak data for user by week
    async getStreakByUser(ctx) {
        const params = ctx.request.body;
        const userId = params?.user_id || ctx.state.user?.id;

        if (!userId) {
            return ctx.badRequest('User ID is required');
        }

        try {
            const rawQuery = `
                WITH RECURSIVE week_days AS (
                    SELECT 0 AS day_offset, DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) AS day
                    UNION ALL
                    SELECT day_offset + 1,
                           DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL day_offset + 1 DAY)
                    FROM week_days
                    WHERE day_offset < 6
                ),
                min_date_activities as (
                    select min(DATE(ua.created_at)) min_created
                    FROM user_activities ua
                    JOIN activities a ON ua.activity_id = a.id
                    WHERE a.activity_type = 'streak' AND ua.user_id = ?
                ),
                streak_data AS (
                    SELECT
                        DATE(a.start_date) AS date_only,
                        ua.is_streak_joined as is_join,
                        ua.document_id,
                        ua.time_spent as time,
                        ua.status
                    FROM activities a
                    JOIN user_activities ua ON a.id = ua.activity_id
                    WHERE a.activity_type = 'streak'
                      AND a.start_date >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                      AND a.start_date < DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY)
                      AND ua.user_id = ?
                )
                SELECT
                    wd.day AS dateInWeek,
                    sd.document_id AS documentId,
                    sd.time,
                    CASE DAYOFWEEK(wd.day)
                        WHEN 2 THEN 'T2'
                        WHEN 3 THEN 'T3'
                        WHEN 4 THEN 'T4'
                        WHEN 5 THEN 'T5'
                        WHEN 6 THEN 'T6'
                        WHEN 7 THEN 'T7'
                        WHEN 1 THEN 'CN'
                    END AS name,
                    m.min_created,
                    CASE
                        WHEN sd.is_join IS NOT NULL THEN sd.is_join
                        WHEN sd.document_id IS NULL AND wd.day >= m.min_created AND wd.day >= CURDATE() THEN NULL
                        WHEN sd.document_id IS NULL AND wd.day < m.min_created THEN NULL
                        WHEN sd.document_id IS NULL AND wd.day >= m.min_created THEN 0
                    END AS isJoin,
                    CASE
                        WHEN wd.day = CURDATE() THEN 1
                        ELSE 0
                    END AS isActive
                FROM week_days wd
                LEFT JOIN streak_data sd ON DATE(sd.date_only) = wd.day
                LEFT JOIN min_date_activities m ON TRUE
                ORDER BY dateInWeek ASC
            `;

            const result = await strapi.db.connection.raw(rawQuery, [userId, userId]);
            ctx.body = {
                data: result[0] || []
            };
        } catch (error) {
            ctx.badRequest('Failed to get streak data', { error: error.message });
        }
    },
    // Get current streak count (consecutive days)
    async getTotalRollup(ctx) {
        const params = ctx.request.body;
        const userId = params?.user_id || ctx.state.user?.id;

        if (!userId) {
            return ctx.badRequest('User ID is required');
        }

        try {
            const rawQuery = `
                SELECT DATE(a.start_date) as dateJoin
                FROM activities a
                JOIN user_activities ua ON a.id = ua.activity_id
                WHERE a.activity_type = 'streak'
                  AND ua.user_id = ?
                  AND ua.is_streak_joined = 1
                ORDER BY a.start_date DESC
            `;

            const result = await strapi.db.connection.raw(rawQuery, [userId]);
            const listStreak = result[0] || [];

            let count = 0;
            let currentDate = new Date();
            currentDate.setHours(0, 0, 0, 0);

            // Check if user joined today
            let localDate = currentDate.toLocaleDateString('en-CA');
            if (listStreak.some(item => item.dateJoin === localDate)) {
                count++;
            }

            // Count consecutive days backwards
            while (true) {
                const yesterday = new Date(currentDate);
                yesterday.setDate(yesterday.getDate() - 1);
                yesterday.setHours(0, 0, 0, 0);
                const formatted = yesterday.toLocaleDateString('en-CA');
                const found = listStreak.some(item => item.dateJoin === formatted);

                if (found) {
                    count++;
                    currentDate = yesterday;
                } else {
                    break;
                }
            }

            ctx.body = {
                data: count
            };
        } catch (error) {
            ctx.badRequest('Failed to get streak count', { error: error.message });
        }
    },
    // Get completion data for an activity
    async getDataFinish(ctx) {
        const params = ctx.request.body;
        const userId = params?.user_id || ctx.state.user?.id;
        const activityId = params?.activity_id || params?.streak_question_id; // backward compatibility

        if (!userId || !activityId) {
            return ctx.badRequest('User ID and Activity ID are required');
        }

        try {
            const rawQuery = `
                SELECT
                    COUNT(*) as count,
                    SUM(CASE WHEN ur.is_correct = 1 THEN 1 ELSE 0 END) as is_correct,
                    SUM(CASE
                        WHEN ur.is_star_point = 1 THEN COALESCE(et.point, 0) * 2
                        ELSE COALESCE(et.point, 0)
                    END) as point,
                    ua.time_spent as time,
                    (SELECT COUNT(*) FROM user_activities ua2
                     JOIN activities a2 ON ua2.activity_id = a2.id
                     WHERE ua2.user_id = ? AND a2.activity_type = 'streak' AND ua2.status = 'completed') as max_streak
                FROM user_responses ur
                JOIN questions q ON ur.question_id = q.id
                JOIN exercise_types et ON q.exercise_type_id = et.id
                JOIN user_activities ua ON ur.activity_id = ua.activity_id AND ur.user_id = ua.user_id
                WHERE ur.user_id = ? AND ur.activity_id = ?
                GROUP BY ua.time_spent
            `;

            const result = await strapi.db.connection.raw(rawQuery, [userId, userId, activityId]);

            ctx.body = {
                data: result[0] || []
            };
        } catch (error) {
            ctx.badRequest('Failed to get completion data', { error: error.message });
        }
    },

    // Save question answer (using new user-response system)
    async saveQuestionAnswer(ctx) {
        const { activity_id, question_id, answer, is_star_point, response_time } = ctx.request.body;
        const userId = ctx.state.user?.id;

        if (!userId) {
            return ctx.badRequest('Authentication required');
        }

        try {
            // Use the user-response controller
            const userResponseController = strapi.controller('api::user-response.user-response');
            return await userResponseController.saveAnswer(ctx);
        } catch (error) {
            ctx.badRequest('Failed to save answer', { error: error.message });
        }
    },

    // Create streak (start activity)
    async createStreak(ctx) {
        const { activity_id } = ctx.request.body;
        const userId = ctx.state.user?.id;

        if (!userId) {
            return ctx.badRequest('Authentication required');
        }

        try {
            // Use the user-activity controller
            const userActivityController = strapi.controller('api::user-activity.user-activity');
            return await userActivityController.startActivity(ctx);
        } catch (error) {
            ctx.badRequest('Failed to create streak', { error: error.message });
        }
    },

    // Update time spent
    async updateTime(ctx) {
        const { documentId, time } = ctx.request.body;
        const userId = ctx.state.user?.id;

        if (!userId) {
            return ctx.badRequest('Authentication required');
        }

        try {
            // Find user activity by documentId
            const userActivity = await strapi.entityService.findOne('api::user-activity.user-activity', documentId);

            if (!userActivity || userActivity.user.id !== userId) {
                return ctx.badRequest('User activity not found');
            }

            // Update time spent
            const updated = await strapi.entityService.update('api::user-activity.user-activity', documentId, {
                data: {
                    time_spent: time
                }
            });

            ctx.body = {
                data: updated,
                meta: {}
            };
        } catch (error) {
            ctx.badRequest('Failed to update time', { error: error.message });
        }
    },

    // Get questions for streak activity
    async getQuestionByStreak(ctx) {
        const activityId = ctx.params.id;

        try {
            const activity = await strapi.entityService.findOne('api::activity.activity', activityId, {
                populate: {
                    questions: {
                        populate: {
                            exercise_type: true,
                            questions_topics: true
                        }
                    }
                }
            });

            if (!activity) {
                return ctx.notFound('Activity not found');
            }

            ctx.body = {
                data: {
                    data: activity.questions || []
                }
            };
        } catch (error) {
            ctx.badRequest('Failed to get questions', { error: error.message });
        }
    }
}));