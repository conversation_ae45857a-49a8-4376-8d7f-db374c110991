/**
 * user-response service
 */

// @ts-nocheck
import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::user-response.user-response' as any, ({ strapi }) => ({
  // Calculate user performance for an activity
  async calculatePerformance(activityId: number, userId: number) {
    const responses: any = await strapi.entityService.findMany('api::user-response.user-response' as any, {
      filters: {
        user: userId,
        activity: activityId
      },
      populate: {
        question: {
          populate: { exercise_type: true }
        }
      }
    });

    if (responses.length === 0) {
      return {
        total_questions: 0,
        answered_questions: 0,
        correct_answers: 0,
        accuracy: 0,
        total_points: 0,
        total_time: 0,
        performance_level: 'not_started'
      };
    }

    const totalQuestions = responses.length;
    const correctAnswers = responses.filter(r => r.is_correct).length;
    const totalPoints = responses.reduce((sum, r) => sum + (r.points_earned || 0), 0);
    const totalTime = responses.reduce((sum, r) => sum + (r.response_time || 0), 0);
    const accuracy = (correctAnswers / totalQuestions) * 100;

    // Determine performance level
    let performanceLevel = 'poor';
    if (accuracy >= 90) performanceLevel = 'excellent';
    else if (accuracy >= 80) performanceLevel = 'good';
    else if (accuracy >= 60) performanceLevel = 'average';

    return {
      total_questions: totalQuestions,
      answered_questions: totalQuestions,
      correct_answers: correctAnswers,
      accuracy,
      total_points: totalPoints,
      total_time: totalTime,
      performance_level: performanceLevel
    };
  },

  // Get user's learning analytics
  async getLearningAnalytics(userId: number, timeframe: string = '30d') {
    let dateFilter = '';
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        const week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFilter = `AND ur.created_at >= '${week.toISOString()}'`;
        break;
      case '30d':
        const month = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        dateFilter = `AND ur.created_at >= '${month.toISOString()}'`;
        break;
      case '90d':
        const quarter = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        dateFilter = `AND ur.created_at >= '${quarter.toISOString()}'`;
        break;
    }

    const rawQuery = `
      SELECT 
        a.activity_type,
        COUNT(*) as total_responses,
        SUM(CASE WHEN ur.is_correct = 1 THEN 1 ELSE 0 END) as correct_responses,
        SUM(ur.points_earned) as total_points,
        AVG(ur.response_time) as avg_response_time,
        DATE(ur.created_at) as response_date
      FROM user_responses ur
      JOIN activities a ON ur.activity_id = a.id
      WHERE ur.user_id = ? ${dateFilter}
      GROUP BY a.activity_type, DATE(ur.created_at)
      ORDER BY response_date DESC
    `;

    const result = await strapi.db.connection.raw(rawQuery, [userId]);
    return result[0] || [];
  },

  // Get weak topics for user
  async getWeakTopics(userId: number, limit: number = 5) {
    const rawQuery = `
      SELECT 
        qt.name as topic_name,
        qt.value as topic_value,
        COUNT(*) as total_questions,
        SUM(CASE WHEN ur.is_correct = 1 THEN 1 ELSE 0 END) as correct_answers,
        (SUM(CASE WHEN ur.is_correct = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100 as accuracy
      FROM user_responses ur
      JOIN questions q ON ur.question_id = q.id
      JOIN questions_questions_topics_lnk qqt ON q.id = qqt.question_id
      JOIN questions_topics qt ON qqt.questions_topic_id = qt.id
      WHERE ur.user_id = ?
      GROUP BY qt.id, qt.name, qt.value
      HAVING COUNT(*) >= 3
      ORDER BY accuracy ASC, total_questions DESC
      LIMIT ?
    `;

    const result = await strapi.db.connection.raw(rawQuery, [userId, limit]);
    return result[0] || [];
  }
}));
