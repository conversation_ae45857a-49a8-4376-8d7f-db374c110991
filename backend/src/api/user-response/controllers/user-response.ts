/**
 * user-response controller
 */

// @ts-nocheck
import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::user-response.user-response' as any, ({ strapi }) => ({
  // Save user answer for activity
  async saveAnswer(ctx) {
    const { activity_id, question_id, answer, is_star_point, response_time } = ctx.request.body;
    const userId = ctx.state.user.id;

    try {
      // Get question and activity info
      const question: any = await strapi.entityService.findOne('api::question.question', question_id, {
        populate: { exercise_type: true }
      });

      const activity: any = await strapi.entityService.findOne('api::activity.activity' as any, activity_id);

      if (!question || !activity) {
        return ctx.badRequest('Question or Activity not found');
      }

      // Check if answer is correct
      const isCorrect = question.correct_answer === answer;

      // Calculate points
      let pointsEarned = 0;
      if (isCorrect && question.exercise_type) {
        pointsEarned = question.exercise_type.point || 0;
        if (is_star_point) {
          pointsEarned *= 2; // Double points with star
        }
      }

      // Check if user already answered this question in this activity
      const existingResponse: any = await strapi.entityService.findMany('api::user-response.user-response' as any, {
        filters: {
          user: userId,
          activity: activity_id,
          question: question_id
        }
      });

      let userResponse;

      if (existingResponse.length > 0) {
        // Update existing response
        userResponse = await strapi.entityService.update('api::user-response.user-response' as any, existingResponse[0].id, {
          data: {
            answer,
            is_correct: isCorrect,
            is_star_point: is_star_point || false,
            response_time,
            points_earned: pointsEarned,
            attempt_number: existingResponse[0].attempt_number + 1
          }
        });
      } else {
        // Create new response
        userResponse = await strapi.entityService.create('api::user-response.user-response' as any, {
          data: {
            user: userId,
            activity: activity_id,
            question: question_id,
            answer,
            is_correct: isCorrect,
            is_star_point: is_star_point || false,
            response_time,
            points_earned: pointsEarned,
            attempt_number: 1
          }
        });
      }

      ctx.body = {
        data: userResponse,
        meta: {
          is_correct: isCorrect,
          points_earned: pointsEarned
        }
      };
    } catch (error) {
      ctx.badRequest('Failed to save answer', { error: error.message });
    }
  },

  // Get user responses for an activity
  async getByActivity(ctx) {
    const { activity_id } = ctx.params;
    const userId = ctx.state.user.id;

    try {
      const responses: any = await strapi.entityService.findMany('api::user-response.user-response' as any, {
        filters: {
          user: userId,
          activity: activity_id
        },
        populate: {
          question: {
            populate: {
              exercise_type: true,
              questions_topics: true
            }
          }
        },
        sort: { createdAt: 'asc' }
      });

      // Calculate summary statistics
      const totalQuestions = responses.length;
      const correctAnswers = responses.filter(r => r.is_correct).length;
      const totalPoints = responses.reduce((sum, r) => sum + (r.points_earned || 0), 0);
      const totalTime = responses.reduce((sum, r) => sum + (r.response_time || 0), 0);

      ctx.body = {
        data: responses,
        meta: {
          summary: {
            total_questions: totalQuestions,
            correct_answers: correctAnswers,
            accuracy: totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0,
            total_points: totalPoints,
            total_time: totalTime
          }
        }
      };
    } catch (error) {
      ctx.badRequest('Failed to fetch responses', { error: error.message });
    }
  },

  // Get streak statistics
  async getStreakStats(ctx) {
    const userId = ctx.state.user.id;

    try {
      const rawQuery = `
        SELECT 
          COUNT(DISTINCT ur.activity_id) as total_activities,
          COUNT(*) as total_responses,
          SUM(CASE WHEN ur.is_correct = 1 THEN 1 ELSE 0 END) as correct_responses,
          SUM(ur.points_earned) as total_points,
          AVG(ur.response_time) as avg_response_time
        FROM user_responses ur
        JOIN activities a ON ur.activity_id = a.id
        WHERE ur.user_id = ? AND a.activity_type = 'streak'
      `;

      const result = await strapi.db.connection.raw(rawQuery, [userId]);
      const stats = result[0]?.[0] || {};

      ctx.body = {
        data: {
          total_activities: stats.total_activities || 0,
          total_responses: stats.total_responses || 0,
          correct_responses: stats.correct_responses || 0,
          accuracy: stats.total_responses > 0 ? (stats.correct_responses / stats.total_responses) * 100 : 0,
          total_points: stats.total_points || 0,
          avg_response_time: stats.avg_response_time || 0
        },
        meta: {}
      };
    } catch (error) {
      ctx.badRequest('Failed to fetch streak stats', { error: error.message });
    }
  }
}));
