/**
 * user-response router
 */

export default {
  routes: [
    // Default CRUD routes
    {
      method: 'GET',
      path: '/user-responses',
      handler: 'user-response.find',
    },
    {
      method: 'GET',
      path: '/user-responses/:id',
      handler: 'user-response.findOne',
    },
    {
      method: 'POST',
      path: '/user-responses',
      handler: 'user-response.create',
    },
    {
      method: 'PUT',
      path: '/user-responses/:id',
      handler: 'user-response.update',
    },
    {
      method: 'DELETE',
      path: '/user-responses/:id',
      handler: 'user-response.delete',
    },

    // Custom routes
    {
      method: 'POST',
      path: '/user-responses/save-answer',
      handler: 'user-response.saveAnswer',
    },
    {
      method: 'GET',
      path: '/user-responses/by-activity/:activity_id',
      handler: 'user-response.getByActivity',
    },
    {
      method: 'GET',
      path: '/user-responses/streak-stats',
      handler: 'user-response.getStreakStats',
    },
  ],
};
