{"kind": "collectionType", "collectionName": "activities", "info": {"singularName": "activity", "pluralName": "activities", "displayName": "Activity", "description": "<PERSON><PERSON><PERSON> động học tập (streak, homework, exam, practice)"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "description": {"type": "text"}, "activity_type": {"type": "enumeration", "enum": ["streak", "homework", "exam", "practice"], "required": true}, "course": {"type": "relation", "relation": "manyToOne", "target": "api::course.course", "inversedBy": "activities"}, "grade": {"type": "relation", "relation": "manyToOne", "target": "api::grade.grade", "inversedBy": "activities"}, "chapter": {"type": "relation", "relation": "manyToOne", "target": "api::chapter.chapter", "inversedBy": "activities"}, "questions": {"type": "relation", "relation": "manyToMany", "target": "api::question.question", "mappedBy": "activities"}, "settings": {"type": "json", "description": "<PERSON><PERSON><PERSON> hình riêng cho từng loại activity"}, "time_limit": {"type": "integer", "description": "<PERSON>hời gian gi<PERSON> hạn (phút)"}, "max_attempts": {"type": "integer", "default": 1, "description": "<PERSON><PERSON> lần thử tối đa"}, "is_active": {"type": "boolean", "default": true}, "start_date": {"type": "datetime", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"type": "datetime", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "user_responses": {"type": "relation", "relation": "oneToMany", "target": "api::user-response.user-response", "mappedBy": "activity"}, "user_activities": {"type": "relation", "relation": "oneToMany", "target": "api::user-activity.user-activity", "mappedBy": "activity"}}}