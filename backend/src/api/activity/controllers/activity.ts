/**
 * activity controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::activity.activity', ({ strapi }) => ({
  // Get activities by type and course
  async findByType(ctx) {
    const { activity_type, course_id } = ctx.query;
    
    try {
      const activities = await strapi.entityService.findMany('api::activity.activity', {
        filters: {
          activity_type,
          course: course_id,
          is_active: true
        },
        populate: {
          course: true,
          grade: true,
          chapter: true,
          questions: {
            populate: {
              exercise_type: true,
              questions_topics: true
            }
          }
        },
        sort: { start_date: 'desc' }
      });

      ctx.body = {
        data: activities,
        meta: {}
      };
    } catch (error) {
      ctx.badRequest('Failed to fetch activities', { error: error.message });
    }
  },

  // Get streak activities for today
  async getTodayStreak(ctx) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    try {
      const streakActivity = await strapi.entityService.findMany('api::activity.activity', {
        filters: {
          activity_type: 'streak',
          start_date: {
            $gte: today.toISOString(),
            $lt: tomorrow.toISOString()
          },
          is_active: true
        },
        populate: {
          course: true,
          questions: {
            populate: {
              exercise_type: true,
              questions_topics: true
            }
          }
        }
      });

      ctx.body = {
        data: streakActivity[0] || null,
        meta: {}
      };
    } catch (error) {
      ctx.badRequest('Failed to fetch today streak', { error: error.message });
    }
  },

  // Create streak activity for specific date
  async createStreakActivity(ctx) {
    const { course_id, date, questions } = ctx.request.body;

    try {
      const activity = await strapi.entityService.create('api::activity.activity', {
        data: {
          title: `Streak - ${date}`,
          activity_type: 'streak',
          course: course_id,
          start_date: new Date(date),
          settings: {
            date: date,
            streak_config: {
              questions_per_session: 3,
              time_limit_per_question: 60
            }
          },
          questions: questions,
          is_active: true
        }
      });

      ctx.body = {
        data: activity,
        meta: {}
      };
    } catch (error) {
      ctx.badRequest('Failed to create streak activity', { error: error.message });
    }
  }
}));
