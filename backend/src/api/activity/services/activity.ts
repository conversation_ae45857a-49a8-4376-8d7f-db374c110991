/**
 * activity service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::activity.activity', ({ strapi }) => ({
  // Get activity with user progress
  async findWithUserProgress(activityId: number, userId: number) {
    const activity = await strapi.entityService.findOne('api::activity.activity', activityId, {
      populate: {
        questions: {
          populate: {
            exercise_type: true,
            questions_topics: true
          }
        },
        user_responses: {
          filters: { user: userId },
          populate: {
            question: true
          }
        },
        user_activities: {
          filters: { user: userId }
        }
      }
    });

    if (!activity) {
      return null;
    }

    // Calculate progress
    const totalQuestions = activity.questions?.length || 0;
    const answeredQuestions = activity.user_responses?.length || 0;
    const progress = totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100 : 0;

    // Get user activity status
    const userActivity = activity.user_activities?.[0];

    return {
      ...activity,
      progress,
      user_status: userActivity?.status || 'not_started',
      user_score: userActivity?.score || 0,
      user_time_spent: userActivity?.time_spent || 0
    };
  },

  // Get streak statistics for user
  async getStreakStats(userId: number) {
    const rawQuery = `
      SELECT 
        COUNT(*) as total_streaks,
        SUM(CASE WHEN ua.status = 'completed' THEN 1 ELSE 0 END) as completed_streaks,
        AVG(ua.score) as average_score,
        MAX(ua.score) as best_score
      FROM activities a
      JOIN user_activities ua ON a.id = ua.activity_id
      WHERE a.activity_type = 'streak' 
        AND ua.user_id = ?
    `;

    const result = await strapi.db.connection.raw(rawQuery, [userId]);
    return result[0]?.[0] || {
      total_streaks: 0,
      completed_streaks: 0,
      average_score: 0,
      best_score: 0
    };
  },

  // Get current streak count
  async getCurrentStreakCount(userId: number) {
    const rawQuery = `
      SELECT DATE(a.start_date) as streak_date
      FROM activities a
      JOIN user_activities ua ON a.id = ua.activity_id
      WHERE a.activity_type = 'streak' 
        AND ua.user_id = ? 
        AND ua.status = 'completed'
      ORDER BY a.start_date DESC
    `;

    const result = await strapi.db.connection.raw(rawQuery, [userId]);
    const streakDates = result[0] || [];

    let currentStreak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    for (const row of streakDates) {
      const streakDate = new Date(row.streak_date);
      const diffTime = currentDate.getTime() - streakDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === currentStreak) {
        currentStreak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }

    return currentStreak;
  }
}));
