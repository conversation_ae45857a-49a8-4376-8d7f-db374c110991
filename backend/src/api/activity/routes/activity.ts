/**
 * activity router
 */

export default {
  routes: [
    // Default CRUD routes
    {
      method: 'GET',
      path: '/activities',
      handler: 'activity.find',
    },
    {
      method: 'GET',
      path: '/activities/:id',
      handler: 'activity.findOne',
    },
    {
      method: 'POST',
      path: '/activities',
      handler: 'activity.create',
    },
    {
      method: 'PUT',
      path: '/activities/:id',
      handler: 'activity.update',
    },
    {
      method: 'DELETE',
      path: '/activities/:id',
      handler: 'activity.delete',
    },

    // Custom routes
    {
      method: 'GET',
      path: '/activities/by-type',
      handler: 'activity.findByType',
    },
    {
      method: 'GET',
      path: '/activities/today-streak',
      handler: 'activity.getTodayStreak',
    },
    {
      method: 'POST',
      path: '/activities/create-streak',
      handler: 'activity.createStreakActivity',
    },
  ],
};
